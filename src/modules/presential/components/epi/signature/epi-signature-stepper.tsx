import { Button } from "@/shared/components/ui/button";

import { Progress } from "@/shared/components/ui/progress";
import { cn } from "@/shared/lib/utils";
import { Check, ChevronLeft, ChevronRight, FileText, Shield, User } from "lucide-react";
import { useEpiSignatureStepper } from "./hooks/use-epi-signature-stepper.hook";
import { ConfirmationStep } from "./steps/confirmation-step";
import { EpiSelectionStep } from "./steps/epi-selection-step";
import { PersonalDataStep } from "./steps/personal-data-step";
import { SignatureStep } from "./steps/signature-step";
import { TermSelectionStep } from "./steps/term-selection";

export interface IEpiSignatureStepperProps {
	className?: string;
}

export const EpiSignatureStepper = ({ className }: IEpiSignatureStepperProps) => {
	const {
		currentStep,
		totalSteps,
		isLoading,
		canProceed,
		isFirstStep,
		isLastStep,
		progress,
		stepConfig,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		handleReset,
		getStepStatus,
		getCurrentStepConfig,
	} = useEpiSignatureStepper();

	const renderStepContent = () => {
		switch (currentStep) {
			case 1:
				return <TermSelectionStep />;
			case 2:
				return <EpiSelectionStep />;
			case 3:
				return <PersonalDataStep />;
			case 4:
				return <SignatureStep />;
			case 5:
				return <ConfirmationStep onReset={handleReset} />;
			default:
				return <TermSelectionStep />;
		}
	};

	const getStepIcon = (stepId: number) => {
		switch (stepId) {
			case 1:
				return FileText;
			case 2:
				return Shield;
			case 3:
				return User;
			case 4:
				return FileText;
			case 5:
				return Check;
			default:
				return FileText;
		}
	};

	return (
		<div className={cn("w-full space-y-2 md:space-y-6 max-w-6xl mx-auto", className)}>
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="space-y-4">
					<div className="flex items-center justify-between">
						<div className="flex gap-4">
							<div className="bg-gray-100 p-2 md:p-3 rounded-lg flex items-center justify-center">
								<FileText className="w-6 h-6 md:w-8 md:h-8 text-primary" />
							</div>
							<div>
								<h1 className="text-xl md:text-2xl font-semibold text-gray-900">Assinatura de EPI</h1>
								<p className="text-sm text-gray-600">
									Etapa {currentStep} de {totalSteps}: {getCurrentStepConfig()?.description}
								</p>
							</div>
						</div>
						<div className="text-right md:block hidden">
							<div className="text-2xl font-bold text-pormade">{Math.round(progress)}%</div>
							<div className="text-xs text-gray-600">Concluído</div>
						</div>
					</div>
					<div className="md:block hidden">
						<Progress value={progress} className="h-2" />
					</div>
				</div>
			</div>
			<div className="hidden md:block p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="flex items-center   justify-between">
					{stepConfig.map((step, index) => {
						const status = getStepStatus(step.id);
						const Icon = getStepIcon(step.id);
						const isClickable = step.id <= currentStep || status === "completed";
						const isFirst = index === 0;
						const isLast = index === stepConfig.length - 1;
						return (
							<>
								<div
									key={step.id}
									className={cn(
										"flex flex-col items-center transition-all duration-200",
										isClickable ? "cursor-pointer hover:scale-105" : "cursor-not-allowed",
										!isFirst && "ml-auto",
										!isLast && "mr-auto"
									)}
									onClick={() => isClickable && handleStepChange(step.id)}
									style={{ minWidth: 120 }}
								>
									<div
										className={cn(
											"w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-200 shadow-lg",
											status === "completed"
												? "bg-pormade border-pormade text-white"
												: status === "active"
												? "bg-white border-pormade text-pormade ring-4 ring-pormade/20 z-10"
												: "bg-gray-100 border-gray-300 text-gray-400"
										)}
									>
										{status === "completed" ? <Check className="w-6 h-6" /> : <Icon className="w-6 h-6" />}
									</div>
									<div className="mt-3 flex flex-col items-center justify-center text-center w-full">
										<p
											className={cn(
												"text-sm font-medium transition-colors",
												status === "active" || status === "completed" ? "text-gray-900" : "text-gray-500"
											)}
										>
											{step.title}
										</p>
										<p
											className={cn(
												"text-xs mt-1 transition-colors",
												status === "active" || status === "completed" ? "text-gray-600" : "text-gray-400"
											)}
										>
											{step.description}
										</p>
									</div>
								</div>
								{!isLast && (
									<div className="flex-1 flex items-center" style={{ minWidth: 96 }}>
										<div
											className={cn(
												"h-[0.5px] rounded-full transition-all duration-300 w-full",
												currentStep > step.id ? "bg-pormade" : "bg-gray-300"
											)}
										/>
									</div>
								)}
							</>
						);
					})}
				</div>
			</div>
			<div className="md:hidden">
				<div className="p-3 sm:p-4 bg-white shadow-lg rounded-lg">
					<div className="mb-3 sm:mb-4 p-3 sm:p-4 bg-pormade/5 border border-pormade/20 rounded-lg">
						<div className="flex items-center gap-3 sm:gap-4">
							<div className="relative flex-shrink-0">
								<div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center border-2 bg-white border-pormade text-pormade ring-2 sm:ring-4 ring-pormade/20 shadow-lg">
									{(() => {
										const Icon = getStepIcon(currentStep);
										return <Icon className="w-5 h-5 sm:w-6 sm:h-6" />;
									})()}
								</div>
								<div className="absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1 w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center text-xs font-bold border-2 border-white bg-pormade text-white">
									{currentStep}
								</div>
							</div>
							<div className="flex-1 min-w-0">
								<div className="flex items-start sm:items-center justify-between flex-col sm:flex-row gap-1 sm:gap-0">
									<div className="flex-1 min-w-0">
										<h3 className="font-semibold text-sm sm:text-base text-gray-900 leading-tight">
											{getCurrentStepConfig()?.mobileTitle || getCurrentStepConfig()?.title}
										</h3>
										<p className="text-xs sm:text-sm mt-0.5 sm:mt-1 text-gray-600 leading-tight">
											{getCurrentStepConfig()?.description}
										</p>
									</div>
									<div className="flex items-center gap-1 flex-shrink-0">
										<div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-pormade rounded-full animate-pulse"></div>
										<span className="text-xs sm:text-sm text-pormade font-medium">Atual</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="space-y-2 sm:space-y-3">
						<div className="flex items-center justify-between text-xs sm:text-sm text-gray-600">
							<span className="font-medium">Progresso</span>
							<span className="font-semibold">{Math.round(progress)}% concluído</span>
						</div>

						<div className="flex items-center justify-between gap-1 sm:gap-2">
							{stepConfig.map((step, idx) => {
								const status = getStepStatus(step.id);
								const Icon = getStepIcon(step.id);
								const isClickable = step.id <= currentStep || status === "completed";
								const isLast = idx === stepConfig.length - 1;

								return (
									<div key={step.id} className="flex items-center flex-1 min-w-0">
										<div
											className={cn(
												"relative flex flex-col items-center transition-all duration-200 w-full",
												isClickable && "cursor-pointer hover:scale-105"
											)}
											onClick={() => isClickable && handleStepChange(step.id)}
										>
											<div
												className={cn(
													"w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200 shadow-sm",
													status === "completed"
														? "bg-pormade border-pormade text-white"
														: status === "active"
														? "bg-pormade border-pormade text-white ring-2 ring-pormade/30"
														: "bg-gray-100 border-gray-300 text-gray-400"
												)}
											>
												{status === "completed" ? (
													<Check className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
												) : (
													<Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
												)}
											</div>
											<span
												className={cn(
													"text-xs sm:text-sm mt-1 sm:mt-1.5 text-center transition-colors leading-tight px-1",
													status === "active" || status === "completed" ? "text-gray-900 font-medium" : "text-gray-500"
												)}
											>
												{step.mobileTitle || step.title}
											</span>
										</div>

										{!isLast && (
											<div className="flex-1 h-[1px] mx-1 sm:mx-2 bg-gray-200 min-w-[8px] sm:min-w-[12px]">
												<div
													className={cn(
														"h-full transition-all duration-300 rounded-full",
														currentStep > step.id ? "bg-pormade" : "bg-transparent"
													)}
													style={{ width: currentStep > step.id ? "100%" : "0%" }}
												/>
											</div>
										)}
									</div>
								);
							})}
						</div>
					</div>
				</div>
			</div>

			{/* Conteúdo do Step */}
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="p-6 md:p-8">
					<div className="min-h-[500px]">{renderStepContent()}</div>
				</div>
			</div>

			{/* Navegação */}
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="p-4">
					<div className="flex flex-col sm:flex-row justify-between items-center gap-4">
						<Button
							variant="outline"
							onClick={handlePreviousStep}
							disabled={isFirstStep || isLoading}
							className="w-full sm:w-auto flex items-center gap-2"
						>
							<ChevronLeft className="w-4 h-4" />
							Anterior
						</Button>
						<div className="flex gap-2">
							<Button variant="outline" onClick={handleReset} disabled={isLoading} className="hidden sm:flex">
								Reiniciar
							</Button>

							{!isLastStep && (
								<Button
									onClick={handleNextStep}
									disabled={!canProceed || isLoading}
									className="w-full sm:w-auto flex items-center gap-2"
								>
									{isLoading ? "Carregando..." : "Próximo"}
									<ChevronRight className="w-4 h-4" />
								</Button>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
